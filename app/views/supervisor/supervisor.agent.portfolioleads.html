<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<table class="table">
				<thead>
					<th class="sortableColumn" ng-repeat="column in displayedColumns">
						<span ng-click='filterChanged(column.field)'>
							{{ column.label }}
							<span ng-show='sortType == "{{ column.field }}" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "{{ column.field }}" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>Current Stage</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="campaignLead in portfolioLeads | itemsPerPage: perPage" total-items="total" current-page="pagination.current">
						<td ng-repeat="column in displayedColumns">
							<span ng-if="column.field === 'lead.dontContactUntil'">{{ formatDate(getObjectPropertyByString(campaignLead, column.field)) }}</span>
							<span ng-if="column.field !== 'lead.dontContactUntil'">{{ getObjectPropertyByString(campaignLead, column.field) }}</span>
						</td>
						<td ng-class="{'text-danger': getCampaignStage(campaignLead) === 'Suppressed'}">{{ getCampaignStage(campaignLead) }}</td>
						<td>
							<uib-dropdown class="btn-group dropdown">
								<button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
									Actions <span class="caret"></span>
								</button>
								<ul class="dropdown-menu" role="menu">
									<li ng-if="loggedInUser.isAdmin || loggedInUser.isSupervisor || loggedInUser.isClientAdmin">
										<a ng-href="/#/admin/leads/{{campaignLead.lead.id}}/analysis">Analyze</a>
									</li>
								</ul>
							</uib-dropdown>
						</td>
					</tr>
					<tr ng-hide="portfolioLeads.length > 0">
						<td colspan="99">No items found</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
		</div>
	</div>
</div>
