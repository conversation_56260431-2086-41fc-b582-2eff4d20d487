'use strict';

angular.module('dialerFrontendApp')
	.controller('AgentPortfolioLeadsCtrl', function ($scope, $rootScope, Agent, moment, _, agentId, filters, title) {
		$rootScope.pageTitle = title.name  + ' | Portfolio Leads';
		$scope.sortType = 'createdAt'
		$scope.sortReverse = true;
		$scope.pagination = {
			current: 1
		};
		$scope.portfolioLeads = [];
		$scope.total = 0;
		$scope.perPage = 20;

		$scope.displayedColumns = [{
			label: 'KAOS Id',
			field: 'id'
		}, {
			label: 'Client Ref',
			field: 'clientRef'
		}, {
			label: 'First Name',
			field: 'first_name'
		}, {
			label: 'Last Name',
			field: 'last_name'
		}, {
			label: 'Phone 1',
			field: 'phone_home'
		}, {
			label: 'Phone 2',
			field: 'phone_work'
		}, {
			label: 'Phone 3',
			field: 'phone_mobile'
		}, {
			label: 'Phone 4',
			field: 'phone_workmobile'
		}, {
			label: 'Contact Delay',
			field: 'dontContactUntil'
		}];

		$scope.getCampaignStage = function (lead) {
            if (lead.suppressions && lead.suppressions.length) {
                var isSuppressed = false
                for (var i = 0; i < lead.suppressions.length; i++) {
                    var sup = lead.suppressions[i];
                    if (sup.actualStartDate) return 'Suppressed'
                }
            }
            if (lead.campaignleads && lead.campaignleads.length) {
                var result = _.findWhere(campaignStages, {
                    id: lead.campaignleads && lead.campaignleads.length ? lead.campaignleads[0].currentCampaignStageId : null
                });

                if (result)
                    return result.name;
                else
                    return 'No Stage';
            } else {
                return 'No Stage';
            }
        };

		$scope.pageChanged = function (newPageNumber) {
			getResultsPage(newPageNumber);
		};

		$scope.filterChanged = function (filter) {
			$scope.sortType = filter;
			$scope.sortReverse = !$scope.sortReverse;
			$scope.pagination.current = 1;
			getResultsPage(1);
		};

		function getResultsPage(pageNumber) {
			Agent.getPortfolioLeads({
				id: agentId,
				page: pageNumber - 1,
				orderby: $scope.sortType,
				dir: $scope.sortReverse ? 'DESC' : 'ASC',
				filters: filters,
				limit: $scope.perPage
			}).$promise.then(function (result) {
				console.log(result);
				$scope.portfolioLeads = result.data.portfolioLeads;
				$scope.total = result.data.count;
			})
		}

		getResultsPage(1);
	})