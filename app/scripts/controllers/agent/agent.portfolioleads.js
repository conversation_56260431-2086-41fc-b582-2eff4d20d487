'use strict';

angular.module('dialerFrontendApp')
	.controller('AgentPortfolioLeadsCtrl', function ($scope, $rootScope, Agent, moment, _, agentId, filters, title) {
		$rootScope.pageTitle = title.name  + ' | Portfolio Leads';
		$scope.sortType = 'lead.id'
		$scope.sortReverse = true;
		$scope.pagination = {
			current: 1
		};
		$scope.portfolioLeads = [];
		$scope.total = 0;
		$scope.perPage = 20;

		$scope.displayedColumns = [{
			label: 'KAOS Id',
			field: 'lead.id'
		}, {
			label: 'Client Ref',
			field: 'lead.clientRef'
		}, {
			label: 'First Name',
			field: 'lead.first_name'
		}, {
			label: 'Last Name',
			field: 'lead.last_name'
		}, {
			label: 'Phone 1',
			field: 'lead.phone_home'
		}, {
			label: 'Phone 2',
			field: 'lead.phone_work'
		}, {
			label: 'Phone 3',
			field: 'lead.phone_mobile'
		}, {
			label: 'Phone 4',
			field: 'lead.phone_workmobile'
		}, {
			label: 'Campaign',
			field: 'campaign.name'
		}, {
			label: 'Reporting Group',
			field: 'skill'
		}, {
			label: 'Lead Type',
			field: 'subSkill'
		}, {
			label: 'Contact Delay',
			field: 'lead.dontContactUntil'
		}, {
			label: 'Current Stage',
			field: 'campaignstage.name'
		}];

		$scope.getCampaignStage = function (campaignLead) {
            // Check for suppressions on the nested lead object
            if (campaignLead.lead && campaignLead.lead.suppressions && campaignLead.lead.suppressions.length) {
                for (var i = 0; i < campaignLead.lead.suppressions.length; i++) {
                    var sup = campaignLead.lead.suppressions[i];
                    if (sup.actualStartDate) return 'Suppressed'
                }
            }

            // Check for current campaign stage from the CampaignLead object
            if (campaignLead.currentCampaignStage) {
                return campaignLead.currentCampaignStage.name;
            } else {
                return 'No Stage';
            }
        };

		$scope.pageChanged = function (newPageNumber) {
			getResultsPage(newPageNumber);
		};

		$scope.filterChanged = function (filter) {
			$scope.sortType = filter;
			$scope.sortReverse = !$scope.sortReverse;
			$scope.pagination.current = 1;
			getResultsPage(1);
		};

		function getResultsPage(pageNumber) {
			Agent.getPortfolioLeads({
				id: agentId,
				page: pageNumber - 1,
				orderby: $scope.sortType,
				dir: $scope.sortReverse ? 'DESC' : 'ASC',
				filters: filters,
				limit: $scope.perPage
			}).$promise.then(function (result) {
				console.log(result);
				$scope.portfolioLeads = result.data.portfolioLeads;
				$scope.total = result.data.count;
			})
		}

		getResultsPage(1);
	})