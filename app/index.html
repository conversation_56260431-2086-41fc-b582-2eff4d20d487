<!doctype html>
<html class="no-js">

<head>
    <meta charset="utf-8">
    <title>KAOS</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width">
    <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->
    <!-- build:css(.) styles/vendor.css -->
    <!-- bower:css -->
    <link rel="stylesheet" href="bower_components/angular-wizard/dist/angular-wizard.min.css" />
    <link rel="stylesheet" href="bower_components/ui-select/dist/select.css" />
    <link rel="stylesheet" href="bower_components/angular-chart.js/dist/angular-chart.css" />
    <link rel="stylesheet" href="bower_components/sweetalert/dist/sweetalert.css" />
    <!-- endbower -->
    <!-- endbuild -->
    <!-- build:css(.tmp) styles/main.css -->
    <link rel="stylesheet" href="styles/bootstrap.css">
    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.5.0/css/font-awesome.css">
    <link rel="stylesheet" href="styles/jquery.gritter.css">
    <link rel="stylesheet" href="styles/jquery.steps.css">
    <link rel="stylesheet" href="bower_components/angular-loading-bar/build/loading-bar.css">
    <link rel="stylesheet" href="styles/animate.css">
    <link rel="stylesheet" href="styles/style.css">
    <link rel="stylesheet" href="bower_components/switchery/dist/switchery.css">
    <link rel="stylesheet" href="styles/angular-form-gen.css">
    <link rel="stylesheet" href="styles/colorpicker.css" />
    <link rel="stylesheet" href="bower_components/angular-wizard/dist/angular-wizard.css" />
    <link rel="stylesheet" href="styles/directives/tags.css" />
    <link rel="stylesheet" href="//ajax.googleapis.com/ajax/libs/jqueryui/1.11.2/themes/smoothness/jquery-ui.css" />
    <link rel="stylesheet" href="bower_components/spinkit/css/spinkit.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jplayer/2.9.2/skin/blue.monday/css/jplayer.blue.monday.min.css" />
    <!-- endbuild -->
</head>

<body ng-app="dialerFrontendApp" style="{{ allowOverflowX ? 'overflow-x: initial;' : '' }}">
    <!--[if lt IE 7]>
        <p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
        <![endif]-->

    <!-- Wrapper-->
    <div id="wrapper" ng-controller="GlobalCtrl as main" style="{{ allowOverflowX ? 'overflow-x: initial;' : '' }}">
        <ui-view />
    </div>


    <!-- build:js(.) scripts/oldieshim.js -->
    <!--[if lt IE 9]>
        <script src="bower_components/es5-shim/es5-shim.js"></script>
        <script src="bower_components/json3/lib/json3.js"></script>
        <![endif]-->
    <!-- endbuild -->


    <script src="scripts/vendor/HackTimer.js"></script>

    <!-- build:js(.) scripts/vendor.js -->
    <!-- bower:js -->
    <script src="bower_components/jquery/dist/jquery.js"></script>
    <script src="bower_components/angular/angular.js"></script>
    <script src="bower_components/angular-animate/angular-animate.js"></script>
    <script src="bower_components/angular-bootstrap/ui-bootstrap-tpls.js"></script>
    <script src="bower_components/angular-file-upload/angular-file-upload.min.js"></script>
    <script src="bower_components/angular-loading-bar/build/loading-bar.js"></script>
    <script src="bower_components/angular-resource/angular-resource.js"></script>
    <script src="bower_components/momentjs/moment.js"></script>
    <script src="bower_components/humanize-duration/humanize-duration.js"></script>
    <script src="bower_components/angular-timer/dist/angular-timer.js"></script>
    <script src="bower_components/angular-tiny-eventemitter/dist/angular-tiny-eventemitter.js"></script>
    <script src="bower_components/angular-ui-router/release/angular-ui-router.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/affix.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/alert.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/button.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/carousel.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/collapse.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/dropdown.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/tab.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/transition.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/scrollspy.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/modal.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/tooltip.js"></script>
    <script src="bower_components/bootstrap-sass-official/assets/javascripts/bootstrap/popover.js"></script>
    <script src="bower_components/bootstrap-ui-datetime-picker/dist/datetime-picker.min.js"></script>
    <script src="bower_components/transitionize/dist/transitionize.js"></script>
    <script src="bower_components/fastclick/lib/fastclick.js"></script>
    <script src="bower_components/switchery/dist/switchery.js"></script>
    <script src="bower_components/ng-switchery/src/ng-switchery.js"></script>
    <script src="bower_components/underscore/underscore.js"></script>
    <script src="bower_components/lodash/dist/lodash.compat.js"></script>
    <script src="bower_components/angular-wizard/dist/angular-wizard.min.js"></script>
    <script src="bower_components/sip.js/dist/sip.js"></script>
    <script src="bower_components/moment/moment.js"></script>
    <script src="bower_components/moment-timezone/builds/moment-timezone-with-data-2010-2020.js"></script>
    <script src="bower_components/ui-select/dist/select.js"></script>
    <script src="bower_components/angular-uuid-service/angular-uuid-service.js"></script>
    <script src="bower_components/Chart.js/Chart.js"></script>
    <script src="bower_components/angular-chart.js/dist/angular-chart.js"></script>
    <script src="bower_components/sweetalert/dist/sweetalert.min.js"></script>
    <script src="bower_components/angularUtils-pagination/dirPagination.js"></script>
    <script src="bower_components/br-validations/releases/br-validations.js"></script>
    <script src="bower_components/string-mask/src/string-mask.js"></script>
    <script src="bower_components/angular-input-masks/angular-input-masks-standalone.min.js"></script>
    <script src="bower_components/angular-audio/app/angular.audio.js"></script>
    <!-- endbower -->
    <!-- endbuild -->


    <script src="bower_components/jquery-json/dist/jquery.json.min.js"></script>


    <script src="https://cdnjs.cloudflare.com/ajax/libs/jplayer/2.9.2/jplayer/jquery.jplayer.min.js"></script>

    <!-- build:js({.tmp,app}) scripts/scripts.js -->
    <script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.11.2/jquery-ui.min.js"></script>
    <script src="scripts/inspinia/jquery.metisMenu.js"></script>
    <script src="scripts/inspinia/jquery.gritter.min.js"></script>
    <script src="scripts/inspinia/inspinia.js"></script>
    <script src="scripts/sipML5/SIPml-api.js"></script>
    <script src="scripts/verto/jquery.FSRTC.js"></script>
    <script src="scripts/verto/jquery.jsonrpcclient.js"></script>
    <script src="scripts/verto/jquery.verto.js"></script>
    <script src="scripts/vendor/papaparse.js"></script>
    <script src="scripts/vendor/bootstrap.colorpicker.js"></script>
    <script src="scripts/vendor/socket.io.js"></script>
    <script src="scripts/vendor/md5.min.js"></script>
    <script src="scripts/app.modules.js"></script>
    <script src="scripts/app.config.js"></script>
    <script src="scripts/constants/constants.js"></script>
    <script src="scripts/directives/directives.js"></script>
    <script src="scripts/directives/eavForm.js"></script>
    <script src="scripts/directives/tags.js"></script>
    <!-- <script src="scripts/directives/leadcallhistory.js"></script> -->
    <script src="scripts/directives/draggableModal.js"></script>
    <script src="scripts/factories/authentication.js"></script>
    <script src="scripts/factories/resources.js"></script>
    <script src="scripts/factories/phone.js"></script>
    <script src="scripts/factories/thirdpartyDI.js"></script>
    <script src="scripts/factories/sweetalert.js"></script>
    <script src="scripts/factories/errors.js"></script>
    <script src="scripts/services/SipJSWebRTCPhone.js"></script>
    <script src="scripts/services/SipML5WebRTCPhone.js"></script>
    <script src="scripts/services/VertoPhone.js"></script>
    <script src="scripts/services/SIPSoftphone.js"></script>
    <script src="scripts/services/MockPhone.js"></script>
    <script src="scripts/services/download.js"></script>
    <script src="scripts/filters/unique.js"></script>
    <script src="scripts/filters/reverse.js"></script>
    <script src="scripts/app.run.js"></script>
    <script src="scripts/controllers/login.js"></script>
    <script src="scripts/controllers/global.js"></script>
    <script src="scripts/controllers/agent/agent.dashboard.js"></script>
    <script src="scripts/controllers/agent/agent.dashboard.forcewrapup.modal.js"></script>
    <script src="scripts/controllers/agent/agent.dashboard.override.js"></script>
    <script src="scripts/controllers/agent/agent.sales.js"></script>
    <script src="scripts/controllers/agent/agent.pledges.js"></script>
    <script src="scripts/controllers/agent/agent.refusals.js"></script>
    <script src="scripts/controllers/agent/agent.callbacks.js"></script>
    <script src="scripts/controllers/agent/agent.callrecords.js"></script>
    <script src="scripts/controllers/agent/agent.portfolioleads.js"></script>
    <script src="scripts/controllers/agent/agent.panicbutton.js"></script>
    <script src="scripts/controllers/agent/agent.item.delete.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.custom.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.callback.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.pledge.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.refusal.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.sale.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.payinvoice.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.payinvoice.v2.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.payinvoice.v3.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.requestinvoice.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.collectionrefusal.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.pledge.v2.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.pledge.v3.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.sale.v2.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.writeoff.modal.js"></script>
    <script src="scripts/controllers/agent/dispositions/agent.disposition.recurring.modal.js"></script>
    <script src="scripts/controllers/agent/editors/agent.editors.callback.js"></script>
    <script src="scripts/controllers/agent/lead/agent.lead.analysis.js"></script>
    <script src="scripts/controllers/admin/client/admin.clients.js"></script>
    <script src="scripts/controllers/admin/client/admin.client.edit.js"></script>
    <script src="scripts/controllers/admin/client/admin.client.leads.js"></script>
    <script src="scripts/controllers/admin/client/admin.client.resetpassword.js"></script>
    <script src="scripts/controllers/admin/campaignproduct/admin.products.js"></script>
    <script src="scripts/controllers/admin/campaignproduct/admin.product.edit.js"></script>
    <script src="scripts/controllers/admin/campaignproduct/admin.product.upload.js"></script>
    <script src="scripts/controllers/admin/datetimeruleset/admin.datetimerulesets.js"></script>
    <script src="scripts/controllers/admin/datetimeruleset/admin.datetimeruleset.edit.js"></script>
    <script src="scripts/controllers/admin/datetimeruleset/admin.datetimeruleset.modal.edit.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaigns.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.calls.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.importleads.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.importchanges.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.importchanges.history.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.removeleadsimporter.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.importtrainingdocs.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.invoices.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.leads.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.leads.search.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.leads.changestage.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.stages.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.messages.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.trainingdocs.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.trainingdocs.modal.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.wizard.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.skills.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.suppressed.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.suppressed.import.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.suppressed.edit.js"></script>
    <script src="scripts/controllers/admin/campaign/admin.campaign.suppressed.history.js"></script>
    <script src="scripts/controllers/admin/campaignstage/admin.campaignstage.edit.js"></script>
    <script src="scripts/controllers/admin/campaignstage/admin.campaignstage.modal.edit.js"></script>
    <script src="scripts/controllers/admin/campaignstage/admin.campaignstage.dtrules.create.js"></script>
    <script src="scripts/controllers/admin/campaignstage/admin.campaignstage.dtrules.edit.js"></script>
    <script src="scripts/controllers/admin/campaignstage/admin.campaignstage.dtrules.process.js"></script>
    <script src="scripts/controllers/admin/campaignstage/admin.campaignstage.dtrules.copy.js"></script>
    <script src="scripts/controllers/admin/campaignstage/admin.campaignstage.agents.modal.edit.js"></script>
    <script src="scripts/controllers/admin/campaignstage/admin.campaignstage.workflow.modal.edit.js"></script>
    <script src="scripts/controllers/admin/campaignstage/admin.campaignstage.leads.js"></script>
    <script src="scripts/controllers/admin/agent/admin.agents.js"></script>
    <script src="scripts/controllers/admin/agent/admin.agent.edit.js"></script>
    <script src="scripts/controllers/admin/agent/admin.agent.modal.edit.js"></script>
    <script src="scripts/controllers/admin/campaigntype/admin.campaigntypes.js"></script>
    <script src="scripts/controllers/admin/campaigntype/admin.campaigntype.edit.js"></script>
    <script src="scripts/controllers/admin/campaigntype/admin.campaigntype.modal.edit.js"></script>
    <script src="scripts/controllers/admin/disposition/admin.dispositions.js"></script>
    <script src="scripts/controllers/admin/disposition/admin.disposition.edit.js"></script>
    <script src="scripts/controllers/admin/disposition/admin.disposition.modal.edit.js"></script>
    <script src="scripts/controllers/admin/callresultfield/admin.callresultfields.js"></script>
    <script src="scripts/controllers/admin/callresultfield/admin.callresultfield.edit.js"></script>
    <script src="scripts/controllers/admin/callresultfield/admin.callresultfieldgroup.edit.js"></script>
    <script src="scripts/controllers/admin/leadfield/admin.leadfield.edit.js"></script>
    <script src="scripts/controllers/admin/skill/admin.skills.js"></script>
    <script src="scripts/controllers/admin/skill/admin.skill.edit.js"></script>
    <script src="scripts/controllers/admin/agentstate/admin.agentstates.js"></script>
    <script src="scripts/controllers/admin/agentstate/admin.agentstate.edit.js"></script>
    <script src="scripts/controllers/admin/lead/admin.lead.modal.edit.js"></script>
    <script src="scripts/controllers/admin/lead/admin.lead.callhistory.js"></script>
    <script src="scripts/controllers/admin/lead/admin.lead.audithistory.js"></script>
    <script src="scripts/controllers/admin/lead/admin.lead.disposition.js"></script>
    <script src="scripts/controllers/admin/lead/admin.lead.analysis.js"></script>
    <script src="scripts/controllers/admin/user/admin.user.edit.js"></script>
    <script src="scripts/controllers/admin/user/admin.users.js"></script>
    <script src="scripts/controllers/admin/refusalreason/admin.refusalreason.edit.js"></script>
    <script src="scripts/controllers/admin/refusalreason/admin.refusalreasons.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.broadcast.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.leadaudit.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.campaigns.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.agents.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.agent.changestatus.modal.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.campaignnotes.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.callrecords.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.callbacks.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.callbacks.search.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.campaign.callattempts.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.campaign.callattempts.v2.js"></script>
    <script src="scripts/controllers/supervisor/supervisor.campaign.audit.js"></script>
    <script src="scripts/controllers/supervisor/dashboard/supervisor.dashboard.campaign.js"></script>
    <script src="scripts/controllers/supervisor/dashboard/supervisor.dashboard.agent.js"></script>
    <script src="scripts/controllers/supervisor/editors/supervisor.editors.sales.js"></script>
    <script src="scripts/controllers/supervisor/editors/supervisor.editors.pledges.js"></script>
    <script src="scripts/controllers/supervisor/editors/supervisor.editors.refusals.js"></script>
    <script src="scripts/controllers/supervisor/editors/supervisor.editors.callback.js"></script>
    <script src="scripts/controllers/report/report.agenttimeline.js"></script>
    <script src="scripts/controllers/report/reports.js"></script>
    <script src="scripts/controllers/report/report.client.js"></script>
    <script src="scripts/controllers/report/report.client.history.js"></script>
    <script src="scripts/controllers/report/report.history.js"></script>
    <script src="scripts/controllers/report/report.builder.js"></script>
    <script src="scripts/controllers/report/filters/report.filters.modal.js"></script>
    <script src="scripts/controllers/report/filters/report.query.filters.js"></script>
    <script src="scripts/controllers/report/email/report.email.js"></script>
    <script src="scripts/controllers/report/schedule/report.schedules.js"></script>
    <script src="scripts/controllers/report/schedule/report.schedule.edit.js"></script>
    <script src="scripts/controllers/report/views/reports.views.js"></script>
    <script src="scripts/controllers/report/views/reports.views.weeklygoals.js"></script>
    <script src="scripts/controllers/report/views/reports.views.weeklygoalsv2.js"></script>
    <script src="scripts/controllers/report/views/reports.views.expiredcallbacks.js"></script>
    <script src="scripts/controllers/report/views/reports.views.pledges.js"></script>
    <script src="scripts/controllers/report/views/reports.views.sales.js"></script>
    <script src="scripts/controllers/report/views/reports.views.collections.js"></script>
    <script src="scripts/controllers/report/views/reports.views.leadchanges.js"></script>
    <script src="scripts/controllers/common/common.dialogmodal.js"></script>
    <script src="scripts/controllers/common/common.changepassword.js"></script>
    <script src="scripts/controllers/system/sessions/system.sessions.js"></script>
    <script src="scripts/controllers/system/system.email.js"></script>
    <script src="scripts/controllers/system/system.devices.js"></script>
    <script src="scripts/controllers/system/system.emailhistory.js"></script>
    <script src="scripts/controllers/system/system.errors.js"></script>
    <script src="scripts/controllers/system/merchants/system.merchants.js"></script>
    <script src="scripts/controllers/system/merchants/system.merchants.edit.js"></script>
    <script src="scripts/controllers/system/merchants/system.merchants.generate.js"></script>
    <script src="scripts/controllers/system/devices/system.devices.edit.js"></script>
    <script src="scripts/controllers/collections/payment/admin.payments.js"></script>
    <script src="scripts/controllers/collections/recurring/admin.recurring.js"></script>
    <script src="scripts/controllers/collections/recurring/admin.recurring.payments.js"></script>
    <script src="scripts/controllers/collections/recurring/edit.js"></script>
    <script src="scripts/controllers/collections/invoices/collections.invoices.js"></script>
    <script src="scripts/controllers/collections/invoices/collections.invoices.exports.js"></script>
    <script src="scripts/controllers/collections/invoices/collections.invoices.payment.js"></script>
    <script src="scripts/controllers/collections/invoices/collections.invoice.history.js"></script>
    <script src="scripts/controllers/collections/invoices/collections.invoice.payments.js"></script>
    <script src="scripts/controllers/collections/invoices/collections.invoices.search.js"></script>
    <script src="scripts/controllers/collections/invoices/collections.invoices.fix.js"></script>
    <script src="scripts/controllers/collections/invoices/collections.invoice.paymentplan.js"></script>
    <script src="scripts/controllers/collections/invoices/collections.invoice.history.changecard.js"></script>


    <script src="scripts/controllers/test/test.payment.js"></script>
    <script src="scripts/controllers/test/test.pledge.v2.js"></script>

    <script type="text/javascript" src="https://cdn.rawgit.com/ricmoo/aes-js/e27b99df/index.js"></script>

    <!-- endbuild -->

    <script type="text/javascript">
        var extScope;
        function tsepHandler(eventType, event) {
            if (extScope && extScope.tsysEvent && typeof extScope.tsysEvent === 'function') {
                extScope.$apply(function () {
                    extScope.tsysEvent(eventType, event)
                });
            }
        }

    </script>
</body>

</html>