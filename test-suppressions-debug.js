const Sequelize = require('sequelize');
const APP_CONFIG = require('./config/constants');

// Create database connection
const db = new Sequelize(APP_CONFIG.DB.schema, APP_CONFIG.DB.user, APP_CONFIG.DB.pass, {
    host: APP_CONFIG.DB.config.host,
    dialect: 'mysql',
    logging: false
});

function testSuppressionsQuery() {
    const campaignId = 123;
    const agentWhere = '';
    
    db.query(`
        SELECT
            cl.currentCampaignStageId as 'stageId',
            l.tfSkillId as 'reportingGroupId',
            l.tfsubskillid as 'skillId',
            COUNT(*) as 'suppressed'
        FROM suppressions s
        INNER JOIN leads l ON l.id = s.leadId
        INNER JOIN campaignleads cl ON cl.leadid = l.id AND cl.campaignid = ${campaignId}
        WHERE s.campaignId = ${campaignId}
            AND s.finished = FALSE
            AND s.actualStartDate IS NOT NULL
            AND l.tfsubskillid IS NOT NULL
            ${agentWhere}
        GROUP BY cl.currentCampaignStageId, l.tfSkillId, l.tfsubskillid WITH ROLLUP
    `, {
        type: Sequelize.QueryTypes.SELECT
    })
    .then(suppressionsData => {
        console.log('Suppressions Query Results:');
        console.log(JSON.stringify(suppressionsData, null, 2));
        
        // Check for lead type level data
        const leadTypeLevelData = suppressionsData.filter(row => 
            row.stageId !== null && row.reportingGroupId !== null && row.skillId !== null
        );
        
        console.log('\nLead Type Level Suppressions (should have counts):');
        console.log(JSON.stringify(leadTypeLevelData, null, 2));
        
        // Check for reporting group level data
        const reportingGroupLevelData = suppressionsData.filter(row => 
            row.stageId !== null && row.reportingGroupId !== null && row.skillId === null
        );
        
        console.log('\nReporting Group Level Suppressions:');
        console.log(JSON.stringify(reportingGroupLevelData, null, 2));
        
        // Check for stage level data
        const stageLevelData = suppressionsData.filter(row => 
            row.stageId !== null && row.reportingGroupId === null && row.skillId === null
        );
        
        console.log('\nStage Level Suppressions:');
        console.log(JSON.stringify(stageLevelData, null, 2));
        
        // Check for grand total
        const grandTotal = suppressionsData.filter(row => 
            row.stageId === null && row.reportingGroupId === null && row.skillId === null
        );
        
        console.log('\nGrand Total Suppressions:');
        console.log(JSON.stringify(grandTotal, null, 2));
        
        process.exit(0);
    })
    .catch(error => {
        console.error('Error:', error);
        process.exit(1);
    });
}

testSuppressionsQuery();
